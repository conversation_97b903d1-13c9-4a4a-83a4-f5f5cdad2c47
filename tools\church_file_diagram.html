<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Church Data Management System</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Church Data Management System</h1>
            <p>Assessment & File Structure Implementation Guide</p>
        </div>

        <div class="controls">
            <button class="btn" onclick="toggleAssessment()">Toggle Assessment</button>
            <button class="btn" onclick="expandAll()">Expand All</button>
            <button class="btn" onclick="collapseAll()">Collapse All</button>
            <button class="btn" onclick="togglePowerChurch()">Toggle PowerChurch Info</button>
        </div>

        <!-- Assessment Section -->
        <div class="assessment-section" id="assessment-section">
            <div class="assessment-header">
                <h2>📋 Digital Asset Assessment</h2>
                <p>Complete this assessment to understand your current file management needs and design a tailored solution.</p>
            </div>
            
            <div class="assessment-content">
                <div class="assessment-intro">
                    <h3>Introduction & Purpose</h3>
                    <p>As a specialized consultant in IT infrastructure and data management for non-profit and religious organizations, this assessment will help establish a highly efficient, secure, and intuitive digital file management system. The insights gathered will be crucial in designing a tailored solution that addresses your unique needs and challenges.</p>
                </div>

                <div class="assessment-category">
                    <h3 onclick="toggleCategory(this)" class="category-header">
                        <span class="category-icon">📁</span>
                        1. Current System Understanding
                    </h3>
                    <div class="category-content">
                        <div class="subsection">
                            <h4>1.1 PowerChurch Usage & Integration Points</h4>
                            <div class="question-list">
                                <div class="question">
                                    <p><strong>Q1:</strong> Which specific PowerChurch modules are currently in use? (e.g., Membership, Contributions, Accounting, Events)</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q2:</strong> What types of information do you store directly within PowerChurch versus external files?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q3:</strong> Are there PowerChurch reports you regularly export and save outside of PowerChurch?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q4:</strong> What are the key pain points with your current PowerChurch implementation?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="subsection">
                            <h4>1.2 Current File Organization & Access</h4>
                            <div class="question-list">
                                <div class="question">
                                    <p><strong>Q1:</strong> Where are your organization's files currently stored? (local computers, cloud services, network drives, etc.)</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q2:</strong> Describe your current folder structure and file naming conventions.</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q3:</strong> How do staff and volunteers currently find files? What challenges do they face?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q4:</strong> What backup procedures are in place for digital files?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q5:</strong> What permissions or access control systems are implemented?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q6:</strong> How does your organization maintain separation of concerns for sensitive data? For example, how do you ensure that volunteers, interns, or staff with limited roles cannot access financial records, donor information, or other confidential materials unless explicitly authorized?</p>
                                    <textarea placeholder="Describe your current practices for data access control, role-based permissions, and any challenges with maintaining appropriate separation of sensitive information..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="subsection">
                            <h4>1.3 Key Pain Points & Challenges</h4>
                            <div class="question-list">
                                <div class="question">
                                    <p><strong>Q1:</strong> What are the biggest frustrations with your current digital file organization?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q2:</strong> Have there been instances where critical files could not be located when needed?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q3:</strong> Do you experience issues with file version control?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q4:</strong> How much time do staff spend searching for files or recreating lost documents?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="assessment-category">
                    <h3 onclick="toggleCategory(this)" class="category-header">
                        <span class="category-icon">👥</span>
                        2. Operational Workflow & Role-Based Access
                    </h3>
                    <div class="category-content">
                        <div class="subsection">
                            <h4>2.1 Document Creation & Management</h4>
                            <div class="question-list">
                                <div class="question">
                                    <p><strong>Q1:</strong> Which departments or roles create the most digital documents?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q2:</strong> What types of documents are created most frequently?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q3:</strong> How are documents typically shared among staff and volunteers?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="subsection">
                            <h4>2.4 Understanding Roles and Access Needs</h4>
                            <div class="question-list">
                                <div class="question">
                                    <p><strong>Q1:</strong> List the primary job titles/roles in your organization.</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q2:</strong> For each role, describe what files they need access to and what level of access they require.</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="assessment-category">
                    <h3 onclick="toggleCategory(this)" class="category-header">
                        <span class="category-icon">🔧</span>
                        3. Technical Considerations & Future Vision
                    </h3>
                    <div class="category-content">
                        <div class="subsection">
                            <h4>3.1 Storage & Remote Access</h4>
                            <div class="question-list">
                                <div class="question">
                                    <p><strong>Q1:</strong> What is your approximate current total digital storage capacity?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q2:</strong> Who needs remote access to files and how is that currently handled?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="subsection">
                            <h4>3.3 Future Needs & Ideal System</h4>
                            <div class="question-list">
                                <div class="question">
                                    <p><strong>Q1:</strong> If you could design an ideal file organization system from scratch, what would its key features be?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                                <div class="question">
                                    <p><strong>Q2:</strong> What metrics would demonstrate that a new file organization system has been successful?</p>
                                    <textarea placeholder="Your answer..."></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="assessment-actions">
                    <button class="btn btn-primary" onclick="saveAssessment()">Save Assessment</button>
                    <button class="btn btn-secondary" onclick="exportAssessment()">Export to WordPad</button>
                    <button class="btn btn-success" onclick="proceedToStructure()">Proceed to File Structure</button>
                </div>
            </div>
        </div>

        <div class="powerchurch-integration" id="powerchurch-info">
            <h3>🔗 PowerChurch Integration Points</h3>
            <ul>
                <li>Membership data remains in PowerChurch - export reports to folder 08</li>
                <li>Financial transactions managed in PowerChurch - export summaries as needed</li>
                <li>Contribution tracking handled by PowerChurch - statements exported to folder 08</li>
                <li>Calendar/scheduling primarily in PowerChurch - event documents in folder 06</li>
                <li>Attendance records in PowerChurch - reports exported when needed</li>
            </ul>
        </div>

        <div class="tree" id="fileTree">
            <div class="folder level-0" onclick="toggleFolder(this)">
                <span class="folder-name">
                    <span class="folder-icon">📁</span>
                    [ORGANIZATION_NAME]/
                </span>
                <div class="description">Root directory for all organizational electronic files</div>
                <div class="subfolder">
                    
                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            01_LITURGY_AND_WORSHIP/
                        </span>
                        <div class="description">Primary worship materials and liturgical resources</div>
                        <div class="subfolder">
                            <div class="file">01_Sunday_Services/</div>
                            <div class="file">02_Music/</div>
                            <div class="file">03_Sermons/</div>
                            <div class="file">04_Liturgical_Calendar/</div>
                            <div class="file">05_Special_Services/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            02_ADMINISTRATION/
                        </span>
                        <div class="description">Governance, policies, and administrative documents</div>
                        <div class="subfolder">
                            <div class="file">01_Policy_and_Governance/</div>
                            <div class="file">02_Meetings/</div>
                            <div class="file">03_Planning/</div>
                            <div class="file">04_Human_Resources/</div>
                            <div class="file">05_Legal/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            03_COMMUNICATIONS/
                        </span>
                        <div class="description">Publications, media, and external communications</div>
                        <div class="subfolder">
                            <div class="file">01_Weekly_Communications/</div>
                            <div class="file">02_Publications/</div>
                            <div class="file">03_Digital_Presence/</div>
                            <div class="file">04_Media/</div>
                            <div class="file">05_Branding/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            04_MINISTRIES/
                        </span>
                        <div class="description">All ministry programs</div>
                        <div class="subfolder">
                            <div class="file">01_Christian_Education/</div>
                            <div class="file">02_Youth/</div>
                            <div class="file">03_Children/</div>
                            <div class="file">04_Adult/</div>
                            <div class="file">05_Seniors/</div>
                            <div class="file">06_Outreach/</div>
                            <div class="file">07_Pastoral_Care/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            05_FACILITIES/
                        </span>
                        <div class="description">Building maintenance and property management</div>
                        <div class="subfolder">
                            <div class="file">01_Buildings/</div>
                            <div class="file">02_Maintenance/</div>
                            <div class="file">03_Projects/</div>
                            <div class="file">04_Equipment/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            06_EVENTS/
                        </span>
                        <div class="description">Special events and community activities</div>
                        <div class="subfolder">
                            <div class="file">01_Church_Year/</div>
                            <div class="file">02_Parish_Events/</div>
                            <div class="file">03_Fundraisers/</div>
                            <div class="file">04_Community_Events/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            07_RESOURCES/
                        </span>
                        <div class="description">Templates and reference materials</div>
                        <div class="subfolder">
                            <div class="file">01_Formation/</div>
                            <div class="file">02_Worship/</div>
                            <div class="file">03_Church_Management/</div>
                            <div class="file">04_Training/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            08_FINANCE/
                        </span>
                        <div class="description">Financial records and accounting documents</div>
                        <div class="subfolder">
                            <div class="file">01_Budgets/</div>
                            <div class="file">02_Financial_Reports/</div>
                            <div class="file">03_Accounting_Records/</div>
                            <div class="file">04_Tax_Documents/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            09_EDUCATION/
                        </span>
                        <div class="description">Educational programs and curriculum</div>
                        <div class="subfolder">
                            <div class="file">01_Sunday_School/</div>
                            <div class="file">02_Adult_Education/</div>
                            <div class="file">03_Confirmation/</div>
                            <div class="file">04_Bible_Study/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            10_OUTREACH/
                        </span>
                        <div class="description">Community outreach and missions</div>
                        <div class="subfolder">
                            <div class="file">01_Community_Programs/</div>
                            <div class="file">02_Mission_Work/</div>
                            <div class="file">03_Social_Services/</div>
                            <div class="file">04_Partnerships/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            11_POWERCHURCH_EXPORTS/
                        </span>
                        <div class="description">Reports exported from PowerChurch software</div>
                        <div class="subfolder">
                            <div class="file">01_Financial_Reports/</div>
                            <div class="file">02_Membership_Reports/</div>
                            <div class="file">03_Contribution_Reports/</div>
                            <div class="file">04_Backup_Files/</div>
                        </div>
                    </div>

                    <div class="folder level-1" onclick="toggleFolder(this)">
                        <span class="folder-name">
                            <span class="folder-icon">📁</span>
                            12_ARCHIVE/
                        </span>
                        <div class="description">Historical records and completed projects</div>
                        <div class="subfolder">
                            <div class="file">2024/</div>
                            <div class="file">2023/</div>
                            <div class="file">2022/</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Assessment Functions
        function toggleAssessment() {
            const assessmentSection = document.getElementById('assessment-section');
            assessmentSection.classList.toggle('show');
        }

        function toggleCategory(element) {
            const content = element.nextElementSibling;
            content.classList.toggle('show');
            
            const icon = element.querySelector('.category-icon');
            if (content.classList.contains('show')) {
                icon.style.transform = 'rotate(90deg)';
            } else {
                icon.style.transform = 'rotate(0deg)';
            }
        }

        function saveAssessment() {
            const answers = {};
            const textareas = document.querySelectorAll('.assessment-section textarea');
            
            textareas.forEach((textarea, index) => {
                const question = textarea.closest('.question').querySelector('p').textContent;
                answers[`question_${index + 1}`] = {
                    question: question,
                    answer: textarea.value
                };
            });
            
            localStorage.setItem('churchAssessment', JSON.stringify(answers));
            alert('Assessment saved successfully!');
        }

        function exportAssessment() {
            const answers = JSON.parse(localStorage.getItem('churchAssessment') || '{}');
            const currentDate = new Date().toLocaleDateString();
            
            let rtfContent = `{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}
\\f0\\fs24 
{\\b\\fs32 Church Digital Asset Assessment}\\par
{\\b Date:} ${currentDate}\\par\\par

{\\b\\fs28 Assessment Responses}\\par\\par
`;

            Object.values(answers).forEach((item, index) => {
                if (item.answer && item.answer.trim()) {
                    rtfContent += `{\\b Question ${index + 1}:} ${item.question.replace(/\*\*/g, '')}\\par
{\\b Answer:} ${item.answer.replace(/\n/g, '\\par ')}\\par\\par
`;
                }
            });

            rtfContent += `\\par
{\\i Generated by Church File Structure Assessment Tool}\\par
}`;

            const blob = new Blob([rtfContent], { type: 'application/rtf' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `Church_Assessment_${currentDate.replace(/\//g, '-')}.rtf`;
            a.click();
            URL.revokeObjectURL(url);
            
            alert('Assessment exported as RTF file! This will open in WordPad with proper formatting.');
        }

        function proceedToStructure() {
            document.getElementById('assessment-section').classList.remove('show');
            alert('Assessment complete! Review the file structure below based on your needs.');
        }

        // File Tree Functions
        function toggleFolder(element) {
            event.stopPropagation();
            
            const subfolder = element.querySelector('.subfolder');
            if (subfolder) {
                subfolder.classList.toggle('show');
                element.classList.toggle('expanded');
            }
        }

        function expandAll() {
            const folders = document.querySelectorAll('.folder');
            const subfolders = document.querySelectorAll('.subfolder');
            
            folders.forEach(folder => folder.classList.add('expanded'));
            subfolders.forEach(subfolder => subfolder.classList.add('show'));
        }

        function collapseAll() {
            const folders = document.querySelectorAll('.folder');
            const subfolders = document.querySelectorAll('.subfolder');
            
            folders.forEach(folder => folder.classList.remove('expanded'));
            subfolders.forEach(subfolder => subfolder.classList.remove('show'));
        }

        function togglePowerChurch() {
            const info = document.getElementById('powerchurch-info');
            if (info.style.display === 'none') {
                info.style.display = 'block';
            } else {
                info.style.display = 'none';
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('assessment-section').classList.add('show');
            
            const savedAssessment = localStorage.getItem('churchAssessment');
            if (savedAssessment) {
                const answers = JSON.parse(savedAssessment);
                const textareas = document.querySelectorAll('.assessment-section textarea');
                
                textareas.forEach((textarea, index) => {
                    const answerKey = `question_${index + 1}`;
                    if (answers[answerKey]) {
                        textarea.value = answers[answerKey].answer;
                    }
                });
            }
            
            const rootFolder = document.querySelector('.level-0');
            if (rootFolder) {
                const subfolder = rootFolder.querySelector('.subfolder');
                if (subfolder) {
                    subfolder.classList.add('show');
                    rootFolder.classList.add('expanded');
                }
            }
        });
    </script>
</body>
</html>
